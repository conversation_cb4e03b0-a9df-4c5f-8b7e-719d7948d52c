import os
import json
import requests
import datetime
import random
import traceback
from fastapi import FastAPI, Request, Response
import uvicorn
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import zhconv
import threading
import base64
import time
from fastapi.middleware.cors import CORSMiddleware

# 模型目录
model_dir = "iic/SenseVoiceSmall"  # 请确保此目录存在并包含模型

# 翻译服务类
class TranslationService:
    """翻译服务，使用 LibreTranslate API 进行文本翻译"""
    
    def __init__(self, base_url="http://**************:5006"):
        """初始化翻译服务
        
        Args:
            base_url: LibreTranslate 服务的基础 URL
        """
        self.base_url = base_url
        self.translate_endpoint = f"{base_url}/translate"
        print(f"初始化翻译服务，连接到: {self.base_url}")
        
    def translate(self, text, source_lang="zh", target_lang="en"):
        """翻译文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言代码 (默认: 'zh' 中文)
            target_lang: 目标语言代码 (默认: 'en' 英文)
            
        Returns:
            翻译后的文本
        """
        if not text or text.strip() == "":
            print("警告: 尝试翻译空文本")
            return ""
            
        try:
            print(f"开始翻译，文本长度: {len(text)}, 源语言: {source_lang}, 目标语言: {target_lang}")
            
            # 对长文本进行分段处理，避免请求超时
            max_length = 1000  # 每段最大字符数
            if len(text) > max_length:
                segments = []
                for i in range(0, len(text), max_length):
                    segments.append(text[i:i+max_length])
                
                translated_segments = []
                for segment in segments:
                    translated_segment = self._translate_segment(segment, source_lang, target_lang)
                    translated_segments.append(translated_segment)
                
                return " ".join(translated_segments)
            else:
                return self._translate_segment(text, source_lang, target_lang)
                
        except Exception as e:
            print(f"翻译过程中出错: {str(e)}")
            print(f"错误详情: {traceback.format_exc()}")
            return f"[翻译失败] {text[:100]}..."  # 返回原文的一部分，并标记翻译失败
    
    def _translate_segment(self, text, source_lang, target_lang):
        """翻译文本片段"""
        try:
            payload = {
                "q": text,
                "source": source_lang,
                "target": target_lang,
                "format": "text",
                "api_key": ""  # 如果需要 API 密钥，请在此处添加
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            print(f"发送翻译请求到: {self.translate_endpoint}")
            response = requests.post(
                self.translate_endpoint,
                headers=headers,
                data=json.dumps(payload),
                timeout=60  # 增加超时时间到60秒
            )
            
            print(f"翻译请求响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                translated_text = result.get("translatedText", "")
                print(f"翻译成功，结果长度: {len(translated_text)}")
                return translated_text
            else:
                print(f"翻译请求失败: {response.status_code}, {response.text}")
                # 尝试解析错误响应
                try:
                    error_info = response.json()
                    print(f"错误详情: {json.dumps(error_info, ensure_ascii=False)}")
                except:
                    print(f"无法解析错误响应: {response.text[:200]}")
                return ""
                
        except requests.exceptions.ConnectionError as e:
            print(f"连接到翻译服务失败: {str(e)}")
            # 尝试使用备用服务器
            if "**************" in self.base_url:
                print("尝试使用本地服务器作为备用")
                backup_url = "http://localhost:5006/translate"
                try:
                    backup_response = requests.post(
                        backup_url,
                        headers=headers,
                        data=json.dumps(payload),
                        timeout=60
                    )
                    if backup_response.status_code == 200:
                        result = backup_response.json()
                        return result.get("translatedText", "")
                except Exception as backup_e:
                    print(f"备用服务器也失败: {str(backup_e)}")
            return ""
        except Exception as e:
            print(f"翻译片段时出错: {str(e)}")
            return ""
    
    def get_supported_languages(self):
        """获取支持的语言列表"""
        try:
            response = requests.get(f"{self.base_url}/languages", timeout=10)
            if response.status_code == 200:
                languages = response.json()
                print(f"支持的语言: {json.dumps(languages, ensure_ascii=False)}")
                return languages
            print(f"获取语言列表失败: {response.status_code}, {response.text}")
            return []
        except Exception as e:
            print(f"获取支持语言时出错: {str(e)}")
            return []

# Minio 上传函数
def upload_to_minio(file_path: str, biz_path: str) -> str:
    """上传文件到 Minio"""
    try:
        url = "http://**************:9033/cdu/sys/upload/uploadMinio"
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        print(f"准备上传文件: {file_path} 到Minio路径: {biz_path}")
        
        # 准备 multipart/form-data 请求
        with open(file_path, 'rb') as f:
            file_content = f.read()  # 读取整个文件内容，避免文件句柄在请求期间保持打开状态
            
            files = {
                'file': (os.path.basename(file_path), file_content, 'application/octet-stream')  # 指定 MIME 类型
            }
            
            params = {
                'bizPath': biz_path  # 设置业务路径，例如 "video"
            }
            
            headers = {
                'X-Access-Token': 'global'  # 替换为实际的认证 token
            }
            
            # 发送 POST 请求
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=headers,
                timeout=60  # 增加超时时间
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                print(f"上传成功，Minio API响应: {json.dumps(result, ensure_ascii=False)}")
                file_url = result.get("data", {}).get("url")  # 从响应中提取文件 URL
                if file_url:
                    print(f"成功获取到文件URL: {file_url}")
                    return file_url
                else:
                    raise ValueError(f"Unexpected response structure: {result}")
            else:
                raise ValueError(f"Upload failed with status code: {response.status_code}, response: {response.text}")
                
    except Exception as e:
        print(f"Error uploading file to Minio: {str(e)}")
        traceback.print_exc()  # 打印完整的错误堆栈
        raise

# FunASR 转录类
class FunASRTranscriber(object):
    def __init__(self, model_dir, gpu_device):
        self.model = AutoModel(
            model=model_dir,
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device=gpu_device
        )
        self.translation_service = TranslationService()

    def transcribe(self, audio_path):
        """通过模型进行音频转录，支持时间戳输出"""
        try:
            print(f"开始转录音频文件: {audio_path}")
            transcription_result = self.model.generate(
                input=audio_path,
                cache={},
                language="auto",  # 自动语言识别
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
                output_timestamp=True  # 启用时间戳输出
            )

            # 优化日志输出，避免记录大量时间戳信息
            if transcription_result and isinstance(transcription_result, list) and len(transcription_result) > 0:
                log_result = transcription_result[0].copy() if isinstance(transcription_result[0], dict) else {}
                
                # 记录时间戳数量而不是详细内容
                if "timestamp" in log_result:
                    timestamp_count = len(log_result["timestamp"])
                    print(f"成功获取到时间戳信息: {timestamp_count} 个词元")
                    # 记录时间戳信息的结构
                    if timestamp_count > 0:
                        print(f"时间戳第一项结构: {log_result['timestamp'][0]}")
                    # 移除时间戳内容，避免日志过长
                    log_result.pop("timestamp")
                    
                # 记录其他关键信息
                print(f"Transcription Result: {json.dumps(log_result, ensure_ascii=False, indent=2)}")
            else:
                print("Transcription Result: Empty or invalid result")

            if not transcription_result or not transcription_result[0].get("text"):
                return "", transcription_result

            text = transcription_result[0]["text"]
            processed_text = rich_transcription_postprocess(text)
            return processed_text, transcription_result
        except IndexError as e:
            # 捕获FunASR模型内部可能出现的IndexError，通常在处理某些音频片段时发生
            print(f"转录过程中捕获到索引错误 (可能由空音频或模型BUG引起): {str(e)}")
            print("将返回空结果以保证程序健壮性。")
            traceback.print_exc()
            return "", []  # 返回空结果，而不是让整个请求失败
        except Exception as e:
            print(f"转录过程中发生错误: {str(e)}")
            traceback.print_exc()
            raise

    def transcribe_base64(self, wav_base64):
        """处理Base64编码的音频数据，支持时间戳输出"""
        try:
            # 解码Base64
            print("开始处理Base64编码的音频数据")
            decode_string = base64.b64decode(wav_base64)
            
            # 生成临时文件名
            curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
                random.uniform(1.1, 8.4))[-5:]

            # 确保目标目录存在
            directory = "/root/lyraChatGLM/temp_ask_wav_path/"
            if not os.path.exists(directory):
                os.makedirs(directory)

            # 拼接文件路径
            wavname = os.path.join(directory, curname + '.wav')
            print(f"临时音频文件路径: {wavname}")

            # 写入音频文件
            with open(wavname, "wb") as wav_file:
                wav_file.write(decode_string)
            
            print(f"音频文件已写入磁盘，大小: {os.path.getsize(wavname)} 字节")

            # 执行转录，启用时间戳
            print("开始转录音频文件")
            text, result = self.transcribe(wavname)
            
            # 转换为简体中文
            text = self.convert_traditional_to_simplified(text)
            print(f"转录结果（简体中文）: {text[:100]}..." if len(text) > 100 else f"转录结果（简体中文）: {text}")

            # 删除临时文件
            try:
                os.remove(wavname)
                print(f"临时音频文件已删除: {wavname}")
            except Exception as remove_error:
                print(f"删除临时音频文件失败: {str(remove_error)}")

            return text, result
        except Exception as e:
            print(f"处理Base64音频数据时出错: {str(e)}")
            traceback.print_exc()
            return "", None

    def convert_traditional_to_simplified(self, text):
        """将繁体中文转换为简体中文"""
        return zhconv.convert(text, 'zh-hans')
    
    def clean_special_tags(self, text):
        """清理文本中的特殊标记

        移除SenseVoice模型输出的控制标记，如语言标识、情感标记等
        """
        import re
        # 移除所有<|xxx|>格式的标记
        cleaned_text = re.sub(r'<\|[^|]+\|>', '', text)
        # 移除连续的标点符号
        cleaned_text = re.sub(r'[。，、；：""''（）][ 。，、；：""''（）]+', lambda m: m.group(0)[0], cleaned_text)
        # 移除开头的标点符号
        cleaned_text = re.sub(r'^[。，、；：""''（）]+', '', cleaned_text)
        # 移除末尾多余的标点符号（保留最后一个）
        if len(cleaned_text) > 1 and cleaned_text[-1] in '。，、；：""''（）' and cleaned_text[-2] in '。，、；：""''（）':
            cleaned_text = cleaned_text[:-1]
        return cleaned_text.strip()

    def merge_tokens_to_sentences(self, token_timestamps, max_duration=5.0, max_chars=50):
        """将token级时间戳合并为句子级字幕

        Args:
            token_timestamps: token级时间戳列表，每项为[token, start_time, end_time]
            max_duration: 单个字幕的最大时长（秒）
            max_chars: 单个字幕的最大字符数

        Returns:
            合并后的句子级时间戳列表
        """
        if not token_timestamps:
            return []

        sentences = []
        current_sentence = ""
        current_start = None
        current_end = None

        print(f"开始合并 {len(token_timestamps)} 个token为句子级字幕")

        for i, (token, start_time, end_time) in enumerate(token_timestamps):
            # 清理token
            clean_token = self.clean_special_tags(token).strip()
            if not clean_token:
                continue

            # 如果是第一个token，初始化当前句子
            if current_start is None:
                current_start = start_time
                current_sentence = clean_token
                current_end = end_time
                continue

            # 检查是否应该开始新句子
            should_start_new = False

            # 条件1：时长超过限制
            if (end_time - current_start) > max_duration:
                should_start_new = True
                print(f"时长超限：{end_time - current_start:.2f}s > {max_duration}s")

            # 条件2：字符数超过限制
            elif len(current_sentence + clean_token) > max_chars:
                should_start_new = True
                print(f"字符超限：{len(current_sentence + clean_token)} > {max_chars}")

            # 条件3：遇到句子结束标点符号
            elif any(punct in clean_token for punct in ['。', '！', '？', '.', '!', '?']):
                # 先合并当前token，然后结束句子
                current_sentence += clean_token
                current_end = end_time
                should_start_new = True
                print(f"遇到句子结束标点：{clean_token}")

            # 条件4：时间间隔过大（可能是停顿）
            elif (start_time - current_end) > 1.0:  # 1秒间隔
                should_start_new = True
                print(f"时间间隔过大：{start_time - current_end:.2f}s")

            if should_start_new:
                # 保存当前句子
                if current_sentence.strip():
                    sentences.append([current_sentence.strip(), current_start, current_end])

                # 开始新句子
                current_sentence = clean_token
                current_start = start_time
                current_end = end_time
            else:
                # 合并到当前句子
                current_sentence += clean_token
                current_end = end_time

        # 添加最后一个句子
        if current_sentence and current_sentence.strip():
            sentences.append([current_sentence.strip(), current_start, current_end])

        print(f"合并完成：{len(token_timestamps)} 个token -> {len(sentences)} 个句子")

        # 输出合并结果的统计信息
        if sentences:
            avg_duration = sum(end - start for _, start, end in sentences) / len(sentences)
            avg_chars = sum(len(text) for text, _, _ in sentences) / len(sentences)
            print(f"平均时长：{avg_duration:.2f}s，平均字符数：{avg_chars:.1f}")

        return sentences
        
    def translate_text(self, text, source_lang="zh", target_lang="en"):
        """翻译文本"""
        # 先清理特殊标记，再进行翻译
        cleaned_text = self.clean_special_tags(text)
        return self.translation_service.translate(cleaned_text, source_lang, target_lang)

    def generate_bilingual_srt(self, chinese_text, base_srt_path, transcription_result=None, target_languages=None):
        """生成中文和其他语言的SRT文件，基于实际时间戳
        
        Args:
            chinese_text: 中文文本
            base_srt_path: SRT文件基础路径（不包含后缀）
            transcription_result: 包含时间戳信息的转录结果
            target_languages: 目标语言代码列表，默认为 ['en', 'th']
            
        Returns:
            字典 {语言代码: SRT文件路径}
        """
        # 如果未指定目标语言，则默认为英语和泰语
        if target_languages is None:
            target_languages = ['en', 'th']
        
        # 语言代码到文件后缀的映射
        language_suffix_map = {
            'en': 'EN',  # 英语
            'th': 'TH',  # 泰语
            'zh': 'CN'   # 中文
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(base_srt_path), exist_ok=True)
        
        # 获取时间戳信息和文本拆分
        timestamps = []
        text_segments = []
        
        # 检查转录结果中是否包含时间戳
        if transcription_result and isinstance(transcription_result, list) and len(transcription_result) > 0:
            if "timestamp" in transcription_result[0] and "text" in transcription_result[0]:
                raw_timestamps = transcription_result[0]["timestamp"]
                full_text = transcription_result[0]["text"]

                # 清理全文中的特殊标记
                full_text = self.clean_special_tags(full_text)

                # 日志输出时间戳数量，而不是详细内容
                if raw_timestamps:
                    print(f"处理时间戳: 共 {len(raw_timestamps)} 个")
                    if len(raw_timestamps) > 0:
                        # 只记录第一个时间戳的格式，用于调试
                        print(f"时间戳格式示例: {raw_timestamps[0]}")

                # 处理时间戳数据
                if raw_timestamps and len(raw_timestamps) > 0:
                    # 检查时间戳的格式
                    first_timestamp = raw_timestamps[0]

                    # 检查是否为模型直接返回的 [token, start_time, end_time] 格式
                    if (len(first_timestamp) == 3 and
                        isinstance(first_timestamp[0], str) and
                        isinstance(first_timestamp[1], (int, float)) and
                        isinstance(first_timestamp[2], (int, float))):

                        print(f"检测到token级时间戳格式，直接使用模型数据。找到 {len(raw_timestamps)} 个token")

                        # 记录原始时间戳范围用于调试
                        if len(raw_timestamps) > 0:
                            first_token = raw_timestamps[0]
                            last_token = raw_timestamps[-1]
                            print(f"原始时间戳范围：{first_token[1]:.3f}s - {last_token[2]:.3f}s")
                            print(f"总时长：{last_token[2] - first_token[1]:.3f}s")

                        # 直接使用模型返回的token-时间戳对应关系
                        token_timestamps = []
                        processed_count = 0
                        for token, start_time, end_time in raw_timestamps:
                            # 清理token中的特殊标记
                            clean_token = self.clean_special_tags(token)
                            # 只有非空token才添加
                            if clean_token.strip():
                                token_timestamps.append([clean_token, start_time, end_time])
                                processed_count += 1
                                # 记录前几个token的详细信息
                                if processed_count <= 3:
                                    print(f"Token {processed_count}: '{clean_token}' [{start_time:.3f}s - {end_time:.3f}s] 时长:{end_time-start_time:.3f}s")

                        print(f"成功处理 {processed_count} 个有效token时间戳")

                        # 将token级时间戳合并为句子级字幕
                        timestamps = self.merge_tokens_to_sentences(token_timestamps)
                        print(f"合并后得到 {len(timestamps)} 个句子级字幕")

                        # 记录合并后的前几个句子信息
                        for i, (sentence, start, end) in enumerate(timestamps[:3]):
                            print(f"句子 {i+1}: '{sentence[:30]}...' [{start:.3f}s - {end:.3f}s] 时长:{end-start:.3f}s")

                    # 检查是否为帧格式 [start_frame, end_frame]（向后兼容）
                    elif (len(first_timestamp) == 2 and
                          all(isinstance(x, (int, float)) for x in first_timestamp)):

                        print(f"检测到帧格式的时间戳，将进行处理。找到 {len(raw_timestamps)} 个时间戳")

                        # 将文本分成与时间戳数量匹配的段落（保留原有逻辑作为备用）
                        import re
                        text_segments = re.split(r'[。，？！.?!,;；]', full_text)
                        text_segments = [seg.strip() for seg in text_segments if seg.strip()]

                        # 确保每个文本段落都经过特殊标记清理
                        text_segments = [self.clean_special_tags(seg) for seg in text_segments]

                        # 如果分段数量与时间戳不匹配，进行调整
                        if len(text_segments) > len(raw_timestamps):
                            # 合并多余的段落
                            print(f"文本分段 ({len(text_segments)}) 多于时间戳 ({len(raw_timestamps)})，合并多余段落")
                            while len(text_segments) > len(raw_timestamps):
                                if len(text_segments) > 1:
                                    text_segments[-2] += text_segments[-1]
                                    text_segments.pop()
                                else:
                                    break
                        elif len(text_segments) < len(raw_timestamps):
                            # 添加空段落
                            print(f"文本分段 ({len(text_segments)}) 少于时间戳 ({len(raw_timestamps)})，添加空段落")
                            text_segments.extend([""] * (len(raw_timestamps) - len(text_segments)))

                        # 将帧转换为秒（使用SenseVoice模型的实际公式）
                        processed_count = 0
                        for i, (start_frame, end_frame) in enumerate(raw_timestamps):
                            if i < len(text_segments):
                                # 转换帧到秒 - 使用SenseVoice模型的实际计算公式
                                start_time = max((start_frame * 60 - 30) / 1000, 0)
                                end_time = max((end_frame * 60 - 30) / 1000, 0)
                                # 再次确保文本没有特殊标记
                                clean_text = self.clean_special_tags(text_segments[i])
                                # 只有非空文本才添加
                                if clean_text.strip():
                                    timestamps.append([clean_text, start_time, end_time])
                                    processed_count += 1

                        print(f"成功创建 {processed_count} 个有效字幕段落")
                    else:
                        print(f"无法识别的时间戳格式: {first_timestamp}")
                else:
                    print("警告: 时间戳列表为空")
            else:
                print("警告: 转录结果中没有时间戳或文本信息")
        
        if not timestamps:
            print("警告: 无法获取或处理时间戳信息，将使用固定时长分配")
            # 回退到原始固定时长分配方法
            return self._generate_bilingual_srt_fixed_duration(chinese_text, base_srt_path, target_languages)
        
        # 生成中文SRT文件
        chinese_srt_path = f"{base_srt_path}_CN.srt"
        self._write_srt_file_with_timestamps(timestamps, chinese_srt_path, is_chinese=True)
        
        # 存储所有生成的SRT文件路径
        srt_paths = {'zh': chinese_srt_path}
        
        # 为每种目标语言生成SRT文件
        for lang_code in target_languages:
            if lang_code not in language_suffix_map:
                print(f"警告: 不支持的语言代码 '{lang_code}'，跳过")
                continue
                
            suffix = language_suffix_map[lang_code]
            target_srt_path = f"{base_srt_path}_{suffix}.srt"
            
            # 翻译并保持相同的时间戳
            translated_timestamps = self._translate_timestamps(timestamps, source_lang="zh", target_lang=lang_code)
            
            # 生成目标语言SRT，使用相同的时间戳
            self._write_srt_file_with_timestamps(translated_timestamps, target_srt_path, is_chinese=False)
            
            # 添加到结果字典
            srt_paths[lang_code] = target_srt_path
        
        return srt_paths
    
    def _translate_timestamps(self, timestamps, source_lang="zh", target_lang="en"):
        """翻译时间戳中的文本，保持时间戳不变"""
        translated_timestamps = []
        
        # 为提高翻译效率，先收集所有文本
        texts = [item[0] for item in timestamps if item[0].strip()]
        if not texts:
            return timestamps  # 如果没有有效文本，直接返回原时间戳
            
        # 确保所有文本都已清理特殊标记
        texts = [self.clean_special_tags(text) for text in texts]
        combined_text = "。".join(texts)
        
        # 翻译整个文本
        translated_text = self.translate_text(combined_text, source_lang, target_lang)
        
        # 将翻译结果拆分回单独的句子
        # 注意：这种拆分方法可能不够精确，因为翻译可能会改变句子数量和顺序
        # 一个更准确的方法是逐句翻译，但会减慢处理速度
        translated_parts = []
        if "." in translated_text:
            translated_parts = translated_text.split(".")
        elif "。" in translated_text:
            translated_parts = translated_text.split("。")
        else:
            # 如果找不到句号，尝试按空格分割
            words = translated_text.split()
            # 尽量平均分配到与原始时间戳相同的数量
            if len(words) >= len(timestamps):
                words_per_segment = max(1, len(words) // len(timestamps))
                for i in range(0, len(words), words_per_segment):
                    segment = " ".join(words[i:i+words_per_segment])
                    if segment:
                        translated_parts.append(segment)
                        if len(translated_parts) >= len(timestamps):
                            break
        
        # 清理和填充翻译后的部分
        translated_parts = [part.strip() for part in translated_parts if part.strip()]
        
        # 处理翻译结果与原始时间戳数量不一致的情况
        if len(translated_parts) < len(timestamps):
            # 如果翻译后的句子数量少于原始时间戳，重复最后一个句子
            last_part = translated_parts[-1] if translated_parts else ""
            translated_parts.extend([last_part] * (len(timestamps) - len(translated_parts)))
        elif len(translated_parts) > len(timestamps):
            # 如果翻译后的句子数量多于原始时间戳，截断
            translated_parts = translated_parts[:len(timestamps)]
        
        # 创建新的时间戳列表，保持原始的时间信息
        for i, (text, start_time, end_time) in enumerate(timestamps):
            if i < len(translated_parts) and text.strip():  # 只翻译非空文本
                # 确保翻译后的文本也没有特殊标记
                clean_translated_text = self.clean_special_tags(translated_parts[i])
                translated_timestamps.append([clean_translated_text, start_time, end_time])
            else:
                # 保持原文，但确保清理特殊标记
                clean_text = self.clean_special_tags(text)
                translated_timestamps.append([clean_text, start_time, end_time])
        
        return translated_timestamps
    
    def _write_srt_file_with_timestamps(self, timestamps, srt_file, is_chinese=True):
        """基于时间戳写入SRT文件

        Args:
            timestamps: 时间戳列表，每项为[text, start_time, end_time]
            srt_file: 输出的SRT文件路径
            is_chinese: 是否为中文
        """
        try:
            print(f"开始写入SRT文件: {srt_file}")
            print(f"待写入字幕条目数: {len(timestamps)}")

            valid_entries = 0
            with open(srt_file, 'w', encoding='utf-8') as f:
                for idx, timestamp in enumerate(timestamps):
                    # 确保timestamp有正确的格式
                    if len(timestamp) != 3:
                        print(f"警告: 跳过格式不正确的时间戳: {timestamp}")
                        continue

                    text, start_time, end_time = timestamp
                    # 确保文本没有特殊标记
                    text = self.clean_special_tags(text)
                    text = text.strip()
                    if not text:
                        continue

                    # 格式化时间戳为SRT格式
                    start_time_srt = self.format_time_to_srt(start_time)
                    end_time_srt = self.format_time_to_srt(end_time)

                    # 记录前几个条目的详细信息
                    if valid_entries < 3:
                        print(f"SRT条目 {valid_entries + 1}: '{text[:30]}...' {start_time_srt} --> {end_time_srt}")

                    # 写入SRT格式
                    f.write(f"{valid_entries + 1}\n")
                    f.write(f"{start_time_srt} --> {end_time_srt}\n")

                    # 根据语言添加适当的标点符号
                    if is_chinese and not text.endswith('。'):
                        f.write(f"{text}。\n\n")
                    elif not is_chinese and not text.endswith('.'):
                        f.write(f"{text}.\n\n")
                    else:
                        f.write(f"{text}\n\n")

                    valid_entries += 1

            print(f"成功写入SRT文件: {srt_file}，共 {valid_entries} 个有效条目")

            # 验证文件是否成功创建
            if os.path.exists(srt_file):
                file_size = os.path.getsize(srt_file)
                print(f"SRT文件大小: {file_size} 字节")
            else:
                print(f"警告: SRT文件未成功创建: {srt_file}")

        except Exception as e:
            print(f"写入SRT文件时出错: {str(e)}")
            traceback.print_exc()

    def _generate_bilingual_srt_fixed_duration(self, chinese_text, base_srt_path, target_languages=None, duration=10.0):
        """使用固定时长分配生成字幕（原始方法，作为备用）"""
        # 如果未指定目标语言，则默认为英语和泰语
        if target_languages is None:
            target_languages = ['en', 'th']
        
        # 语言代码到文件后缀的映射
        language_suffix_map = {
            'en': 'EN',  # 英语
            'th': 'TH',  # 泰语
        }
        
        # 生成中文文件路径
        chinese_srt_path = f"{base_srt_path}_CN.srt"
        
        # 将中文文本分割成句子
        chinese_lines = [line.strip() for line in chinese_text.split('。') if line.strip()]
        
        # 生成中文 SRT
        self._write_srt_file(chinese_lines, chinese_srt_path, duration, is_chinese=True)
        
        # 存储所有生成的SRT文件路径
        srt_paths = {'zh': chinese_srt_path}
        
        # 为每种目标语言生成SRT文件
        for lang_code in target_languages:
            if lang_code not in language_suffix_map:
                print(f"警告: 不支持的语言代码 '{lang_code}'，跳过")
                continue
                
            suffix = language_suffix_map[lang_code]
            target_srt_path = f"{base_srt_path}_{suffix}.srt"
            
            # 为每个中文句子单独翻译
            translated_lines = []
            for line in chinese_lines:
                translated_line = self.translate_text(line + "。", source_lang="zh", target_lang=lang_code)
                translated_line = translated_line.replace("。.", ".").replace("。", ".")
                if translated_line.endswith('.'):
                    translated_line = translated_line[:-1]
                translated_lines.append(translated_line)
            
            # 生成目标语言 SRT，使用相同的行数和时间戳
            self._write_srt_file(translated_lines, target_srt_path, duration, is_chinese=False)
            
            # 添加到结果字典
            srt_paths[lang_code] = target_srt_path
        
        return srt_paths
    
    def _write_srt_file(self, lines, srt_file, duration=10.0, is_chinese=True):
        """写入固定时长的SRT文件（原始方法，作为备用）"""
        start_time = 0.0
        
        with open(srt_file, 'w', encoding='utf-8') as f:
            for idx, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                end_time = start_time + duration
                start_time_srt = self.format_time_to_srt(start_time)
                end_time_srt = self.format_time_to_srt(end_time)
                
                f.write(f"{idx + 1}\n")
                f.write(f"{start_time_srt} --> {end_time_srt}\n")
                
                # 添加适当的标点符号
                if is_chinese:
                    f.write(f"{line}{'。' if not line.endswith('。') else ''}\n\n")
                else:
                    f.write(f"{line}{'.' if not line.endswith('.') else ''}\n\n")
                
                start_time = end_time

    def format_time_to_srt(self, seconds):
        """将秒转换为 SRT 格式时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"

# 创建 FastAPI 应用
def create_app(gpu_device):
    app = FastAPI()

    # 添加跨域中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    transcriber = FunASRTranscriber(model_dir, gpu_device)

    @app.post("/api/sound/asr_turbo")
    async def asr_turbo(request: Request):
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        s_time = time.time()
        try:
            json_post_raw = await request.json()
            print("Received JSON:", json_post_raw)

            if 'wavurl' not in json_post_raw:
                raise ValueError("缺少 'wavurl' 字段")

            wavurl = json_post_raw['wavurl']
            
            # 如果未提供target_languages参数，默认生成英语和泰语
            all_supported_languages = ['en', 'th']  # 只保留英语和泰语
            target_languages = json_post_raw.get('target_languages', all_supported_languages)
            
            # 确保 target_languages 是列表
            if isinstance(target_languages, str):
                target_languages = [target_languages]

            # 下载音频文件
            print(f"开始下载音频文件: {wavurl}")
            response = requests.get(wavurl)
            if response.status_code != 200:
                raise ValueError(f"无法下载音频文件，状态码: {response.status_code}")

            curname = f"{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{random.uniform(1.1, 8.4):.5f}"
            wavname = os.path.join("/data/lyraChatGLM/temp_ask_wav_path/", curname + '.wav')
            srt_base_path = os.path.join("/data/lyraChatGLM/srt_path/", curname)

            os.makedirs("/data/lyraChatGLM/temp_ask_wav_path/", exist_ok=True)
            os.makedirs("/data/lyraChatGLM/srt_path/", exist_ok=True)

            print(f"将音频内容写入临时文件: {wavname}")
            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)
            
            print(f"音频文件已保存，大小: {os.path.getsize(wavname)} 字节")

            # 转录，启用时间戳输出
            print("开始音频转录...")
            text, transcription_result = transcriber.transcribe(wavname)
            simplified_text = transcriber.convert_traditional_to_simplified(text)
            print(f"音频转录完成，文本长度: {len(simplified_text)}")

            # 生成多语言 SRT 文件，传递转录结果中的时间戳信息
            print(f"开始生成多语言SRT文件，目标语言: {target_languages}")
            srt_paths = transcriber.generate_bilingual_srt(
                simplified_text,
                srt_base_path, 
                transcription_result=transcription_result, 
                target_languages=target_languages
            )
            print(f"SRT文件生成完成: {list(srt_paths.keys())}")

            # 上传到 Minio 并收集 URL
            srt_urls = {}
            for lang_code, srt_path in srt_paths.items():
                try:
                    # 确保文件完全写入磁盘
                    os.sync()
                    time.sleep(0.5)  # 短暂等待，确保文件已完全写入
                    
                    print(f"开始上传 {lang_code} SRT 文件: {srt_path}")
                    minio_url = upload_to_minio(srt_path, "video")
                    srt_urls[lang_code] = minio_url
                    print(f"{lang_code} SRT 文件已上传到 Minio，URL: {minio_url}")
                    
                    # 上传成功后删除本地SRT文件
                    try:
                        # 确保没有其他进程打开该文件
                        os.sync()
                        time.sleep(0.5)  # 等待确保上传完成
                        
                        # 检查文件是否存在
                        if os.path.exists(srt_path):
                            os.remove(srt_path)
                            print(f"已成功删除本地SRT文件: {srt_path}")
                        else:
                            print(f"警告: 尝试删除不存在的文件: {srt_path}")
                    except Exception as remove_error:
                        print(f"删除本地SRT文件失败: {str(remove_error)}")
                        traceback.print_exc()  # 打印完整的错误堆栈
                except Exception as upload_error:
                    print(f"上传 {lang_code} SRT 文件失败: {str(upload_error)}")
                    traceback.print_exc()  # 打印完整的错误堆栈
                    srt_urls[lang_code] = ""

            # 删除音频文件
            try:
                if os.path.exists(wavname):
                    os.remove(wavname)
                    print(f"已成功删除临时音频文件: {wavname}")
                else:
                    print(f"警告: 尝试删除不存在的音频文件: {wavname}")
            except Exception as remove_error:
                print(f"删除临时音频文件失败: {str(remove_error)}")
                traceback.print_exc()

            # 构建结果
            result = {'text': simplified_text}
            
            # 添加各语言的 SRT URL
            language_suffix_map = {
                'zh': 'cn_srturl',
                'en': 'en_srturl',
                'th': 'th_srturl',
            }
            
            for lang_code, url_key in language_suffix_map.items():
                if lang_code in srt_urls:
                    result[url_key] = srt_urls[lang_code]
            
            # 如果有时间戳信息，添加时间戳数量而非详细内容
            if transcription_result and isinstance(transcription_result, list) and len(transcription_result) > 0:
                if "timestamp" in transcription_result[0]:
                    raw_timestamps = transcription_result[0]["timestamp"]
                    result["timestamps_count"] = len(raw_timestamps)

                    # 检测时间戳格式并添加调试信息
                    if raw_timestamps:
                        first_ts = raw_timestamps[0]
                        last_ts = raw_timestamps[-1]

                        # 检查时间戳格式
                        if (len(first_ts) == 3 and isinstance(first_ts[0], str)):
                            # Token级时间戳格式 [token, start_time, end_time]
                            result["timestamp_format"] = "token_level"
                            result["timestamp_range"] = {
                                "start_time": first_ts[1],
                                "end_time": last_ts[2],
                                "duration_seconds": last_ts[2] - first_ts[1],
                                "first_token": first_ts[0],
                                "last_token": last_ts[0]
                            }
                            print(f"API调试: 检测到token级时间戳，范围 {first_ts[1]:.3f}s - {last_ts[2]:.3f}s")
                        elif (len(first_ts) == 2 and all(isinstance(x, (int, float)) for x in first_ts)):
                            # 帧级时间戳格式 [start_frame, end_frame]
                            result["timestamp_format"] = "frame_level"
                            start_time = max((first_ts[0] * 60 - 30) / 1000, 0)
                            end_time = max((last_ts[1] * 60 - 30) / 1000, 0)
                            result["timestamp_range"] = {
                                "start_frame": first_ts[0],
                                "end_frame": last_ts[1],
                                "start_time": start_time,
                                "end_time": end_time,
                                "duration_seconds": end_time - start_time
                            }
                            print(f"API调试: 检测到帧级时间戳，转换后范围 {start_time:.3f}s - {end_time:.3f}s")
                        else:
                            result["timestamp_format"] = "unknown"
                            result["timestamp_sample"] = str(first_ts)
                            print(f"API调试: 未知时间戳格式: {first_ts}")
            
            res_dict["result"] = result

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"]["ErrorDetails"] = f"详细信息: {traceback.format_exc()}"
            print(f"API处理出错: {str(e)}")
            traceback.print_exc()

        e_time = time.time()
        res_dict["Debug"]["TimeInfo"]["ApiTime"] = "{:.2f}".format(e_time - s_time)
        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    @app.post("/api/sound/asr_turbo_base64")
    async def asr_turbo_base64(request: Request):
        """处理Base64编码的音频数据的API端点"""
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
            "InfoMessage": "已接收",
            "Debug": {
                "ErrorDetails": "",
                "TimeInfo": {
                    "ApiTime": ""
                }
            }
        }

        s_time = time.time()
        try:
            json_post_raw = await request.json()
            print("接收到Base64编码的音频请求")
            
            if 'wavBase64' not in json_post_raw:
                raise ValueError("缺少 'wavBase64' 字段")
            
            # 检查Base64编码是否有效
            try:
                wav_base64 = json_post_raw['wavBase64']
                # 尝试解码前几个字符，验证是否为有效的Base64
                base64.b64decode(wav_base64[:100])
                print(f"Base64数据有效，长度: {len(wav_base64)}")
            except Exception as base64_error:
                raise ValueError(f"无效的Base64编码: {str(base64_error)}")
            
            # 转录音频，并获取时间戳信息
            print("开始转录Base64编码的音频...")
            text, result = transcriber.transcribe_base64(wav_base64)
            print(f"Base64音频转录完成，文本长度: {len(text)}")
            
            # 构建包含文本和时间戳统计的结果
            resp_result = {"text": text}
            
            # 如果有时间戳信息，添加时间戳数量而非详细内容
            if result and isinstance(result, list) and len(result) > 0:
                if "timestamp" in result[0]:
                    # 只记录时间戳数量
                    resp_result["timestamps_count"] = len(result[0]["timestamp"])
                    # 添加简单的统计信息
                    if result[0]["timestamp"]:
                        # 提取第一个和最后一个时间戳的开始和结束时间
                        first_ts = result[0]["timestamp"][0]
                        last_ts = result[0]["timestamp"][-1]
                        resp_result["timestamp_range"] = {
                            "start_frame": first_ts[0],
                            "end_frame": last_ts[1],
                            "duration_seconds": max(((last_ts[1] * 60 - 30) - (first_ts[0] * 60 - 30)) / 1000, 0)
                        }
            
            res_dict["result"] = resp_result

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"]["ErrorDetails"] = f"详细信息: {traceback.format_exc()}"
            print(f"Base64 API处理出错: {str(e)}")
            traceback.print_exc()

        e_time = time.time()
        res_dict["Debug"]["TimeInfo"]["ApiTime"] = "{:.2f}".format(e_time - s_time)
        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    @app.post("/api/sound/download_and_convert")
    async def download_and_convert_to_base64(request: Request):
        """从URL下载音频文件并将其转换为Base64编码"""
        res_dict = {
            "Status": 200,
            "success": True,
            "result": {},
            "ErrorMessage": "无",
        }

        try:
            json_post_raw = await request.json()
            audio_url = json_post_raw.get("wavurl")
            if not audio_url:
                raise ValueError("必须提供 'wavurl' 参数")

            # 下载音频文件
            response = requests.get(audio_url)
            if response.status_code != 200:
                raise ValueError(f"无法下载音频文件，HTTP状态码: {response.status_code}")

            # 保存临时音频文件
            curname = "".join(str(datetime.datetime.now())).replace(' ', '_').replace(':', '_') + '_' + str(
                random.uniform(1.1, 8.4))[-5:]
            wavname = os.path.join("/tmp", curname + '.wav')
            with open(wavname, "wb") as wav_file:
                wav_file.write(response.content)

            # 读取音频文件并转换为Base64编码
            with open(wavname, "rb") as audio_file:
                base64_audio = base64.b64encode(audio_file.read()).decode('utf-8')

            # 返回结果
            res_dict["result"]["base64"] = base64_audio

            # 删除临时文件
            try:
                os.remove(wavname)
            except:
                pass

        except Exception as e:
            res_dict["success"] = False
            res_dict["ErrorMessage"] = f"发生错误: {str(e)}"
            res_dict["Debug"] = traceback.format_exc()

        return Response(content=json.dumps(res_dict, ensure_ascii=False), media_type='application/json;charset=utf-8')

    return app

# 启动服务
def run_app(port, gpu_device):
    app = create_app(gpu_device)
    uvicorn.run(app, host='0.0.0.0', port=port, reload=False)

if __name__ == '__main__':
    thread1 = threading.Thread(target=run_app, args=(10097, "cuda:1"))
    thread2 = threading.Thread(target=run_app, args=(10098, "cuda:1"))
    # 新增两个线程，使用端口10093和10094
    thread3 = threading.Thread(target=run_app, args=(10093, "cuda:1"))
    thread4 = threading.Thread(target=run_app, args=(10094, "cuda:1"))

    thread1.start()
    thread2.start()
    thread3.start()
    thread4.start()

    thread1.join()
    thread2.join()
    thread3.join()
    thread4.join()
