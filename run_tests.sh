#!/bin/bash

echo "🚀 开始运行时间戳修复测试"
echo "=================================="

# 检查Python环境
echo "🐍 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装或不可用"
    exit 1
fi

# 检查测试文件
echo "📁 检查测试文件..."
if [ ! -f "./test.mp3" ]; then
    echo "❌ 测试文件 test.mp3 不存在"
    exit 1
fi
echo "✅ 测试文件存在"

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import librosa, funasr" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  某些依赖可能缺失，但继续测试..."
fi

# 创建输出目录
mkdir -p test_output
mkdir -p quick_test_output

echo ""
echo "🎯 选择测试模式:"
echo "1. 快速测试 (推荐)"
echo "2. 完整测试"
echo "3. 两个都运行"
echo ""
read -p "请选择 (1/2/3): " choice

case $choice in
    1)
        echo "🏃 运行快速测试..."
        python3 quick_test.py
        ;;
    2)
        echo "🔍 运行完整测试..."
        python3 test_timestamp_fix.py
        ;;
    3)
        echo "🏃 运行快速测试..."
        python3 quick_test.py
        echo ""
        echo "🔍 运行完整测试..."
        python3 test_timestamp_fix.py
        ;;
    *)
        echo "❌ 无效选择，运行快速测试..."
        python3 quick_test.py
        ;;
esac

echo ""
echo "📊 测试结果文件:"
echo "   快速测试输出: ./quick_test_output/"
echo "   完整测试输出: ./test_output/"
echo ""
echo "🎉 测试完成!"
